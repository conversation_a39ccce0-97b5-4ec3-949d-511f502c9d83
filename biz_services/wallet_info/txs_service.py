import aiohttp
from datetime import datetime, timedelta
import os


class WalletTxsService:
    @staticmethod
    async def get_tx_counts_by_time(address:str, hours: int=24) -> dict:
        """
        获取钱包过去一段时间内的交易次数

        Args:
            address: 钱包地址
            hours: 小时数
        """
        # 获取单页
        async def fetch_page(address, cursor):
            chain_name = "eth"
            # 计算时间
            from_date = (datetime.now() - timedelta(hours=hours)).strftime("%Y-%m-%d")
            url = f"https://deep-index.moralis.io/api/v2.2/wallets/{address}/history?chain={chain_name}&from_date={from_date}"
            headers = {
                "accept": "application/json",
                "X-API-Key": os.getenv('MORALIS_API_KEY', "")
            }
            params = {}
            if cursor:
                params["cursor"] = cursor
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, headers=headers, params=params) as resp:
                        if resp.status != 200:
                            raise Exception(f"Moralis API Error: {resp.status}")
                        raw_data = await resp.json()

                return raw_data
            except Exception as e:
                return {
                    "status": 500,
                    "error": str(e)
                }

        all_transactions = []
        cursor = None
        while True:
            data = await fetch_page(address, cursor)
            # 收集交易
            txs = data.get("result", [])
            all_transactions.extend(txs)
            # 是否还有下一页
            cursor = data.get("cursor")
            if not cursor:
                break
            # 小延迟，避免 API 速率限制
            #time.sleep(0.2)
        return {
            "status": 200,
            "data": {
                "tx_counts": len(all_transactions)
            }
        }