version: 1

apps:
  - appID: account
    appDirPath: ../
    appPort: 9053
    command: ["/bin/bash", "-c", "go run main.go"]
    env:
      SERVER_HOST: "0.0.0.0"
      SERVER_PORT: "9053"
      LOG_LEVEL: "debug"
      # JWT相关配置
      JWT_EXPIRATION_HOURS: "24"
      JWT_ISSUER: "alphafi"
      JWT_DAPR_SECRET_STORE: "secretstore"
      JWT_SECRET_KEY_NAME: "jwt-secret"
      
    # Dapr sidecar配置
    daprHTTPPort: 3573
    daprGRPCPort: 35073
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6163
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

    # Dapr组件配置
    resourcesPath: "./dapr/components"