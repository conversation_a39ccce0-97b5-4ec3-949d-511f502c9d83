package config

import (
	"os"
	"strconv"
)

// Config 应用配置结构体
type Config struct {
	Server   ServerConfig   `json:"server"`
	Ethereum EthereumConfig `json:"ethereum"`
	Log      LogConfig      `json:"log"`
	JWT      JWTConfig      `json:"jwt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         string `json:"port"`
	Host         string `json:"host"`
	ReadTimeout  int    `json:"readTimeout"`
	WriteTimeout int    `json:"writeTimeout"`
	Mode         string `json:"mode"` // debug, release, test
}

// EthereumConfig 以太坊相关配置
type EthereumConfig struct {
	NetworkID     int64  `json:"networkId"`
	ChainID       int64  `json:"chainId"`
	MessagePrefix string `json:"messagePrefix"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `json:"level"`
	Format string `json:"format"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	ExpirationHours int    `json:"expirationHours"` // JWT过期时间（小时）
	Issuer          string `json:"issuer"`          // JWT签发者
	DaprSecretStore string `json:"daprSecretStore"` // Dapr Secret Store组件名称
	SecretKeyName   string `json:"secretKeyName"`   // 在Secret Store中的密钥名称
}

// LoadConfig 加载配置
func LoadConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port:         getEnv("SERVER_PORT", "8000"),
			Host:         getEnv("SERVER_HOST", "0.0.0.0"),
			ReadTimeout:  getEnvAsInt("SERVER_READ_TIMEOUT", 30),
			WriteTimeout: getEnvAsInt("SERVER_WRITE_TIMEOUT", 30),
			Mode:         getEnv("GIN_MODE", "debug"),
		},
		Ethereum: EthereumConfig{
			NetworkID:     getEnvAsInt64("ETH_NETWORK_ID", 1),
			ChainID:       getEnvAsInt64("ETH_CHAIN_ID", 1),
			MessagePrefix: getEnv("ETH_MESSAGE_PREFIX", "\x19Ethereum Signed Message:\n"),
		},
		Log: LogConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
		JWT: JWTConfig{
			ExpirationHours: getEnvAsInt("JWT_EXPIRATION_HOURS", 24),
			Issuer:          getEnv("JWT_ISSUER", "shovel-account-service"),
			DaprSecretStore: getEnv("JWT_DAPR_SECRET_STORE", "secretstore"),
			SecretKeyName:   getEnv("JWT_SECRET_KEY_NAME", "jwt-secret"),
		},
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为int，如果不存在或转换失败则返回默认值
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsInt64 获取环境变量并转换为int64，如果不存在或转换失败则返回默认值
func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}
