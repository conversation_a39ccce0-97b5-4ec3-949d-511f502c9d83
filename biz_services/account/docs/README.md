# 账号服务 (Account Service)

基于Go语言和Gin框架的用户账号微服务，主要提供以太坊消息签名验证的登录功能。

## 功能特性

- ✅ 以太坊消息签名验证
- ✅ RESTful API接口
- ✅ 统一错误处理
- ✅ 请求日志记录
- ✅ 跨域支持 (CORS)
- ✅ 优雅关闭
- ✅ 配置管理

## 技术栈

- **语言**: Go 1.23.6
- **Web框架**: Gin
- **以太坊库**: go-ethereum
- **配置管理**: 环境变量

## 项目结构

```
biz_services/account/
├── main.go                     # 主程序入口
├── go.mod                      # Go模块文件
├── README.md                   # 项目文档
└── internal/
    ├── config/
    │   └── config.go           # 配置管理
    ├── handlers/
    │   └── auth.go             # 认证处理器
    ├── middleware/
    │   └── error.go            # 中间件
    ├── models/
    │   └── auth.go             # 数据模型
    └── services/
        └── ethereum.go         # 以太坊服务
```

## API接口

### 登录验证

**POST** `/api/v1/auth/login`

#### 请求体
```json
{
    "signature": "0x...",           // 以太坊消息签名 (必需)
    "message": "登录消息内容",        // 原始消息内容 (必需)
    "address": "0x..."              // 以太坊地址 (可选)
}
```

#### 成功响应
```json
{
    "success": true,
    "message": "登录验证成功",
    "data": {
        "address": "0x...",
        "loginTime": **********
    }
}
```

#### 失败响应
```json
{
    "success": false,
    "message": "签名验证失败",
    "code": 401
}
```

### 健康检查

**GET** `/health`

```json
{
    "status": "ok",
    "service": "account"
}
```

## 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SERVER_PORT` | `8080` | 服务端口 |
| `SERVER_HOST` | `0.0.0.0` | 服务主机 |
| `SERVER_READ_TIMEOUT` | `30` | 读取超时(秒) |
| `SERVER_WRITE_TIMEOUT` | `30` | 写入超时(秒) |
| `GIN_MODE` | `debug` | Gin模式 (debug/release/test) |
| `ETH_NETWORK_ID` | `1` | 以太坊网络ID |
| `ETH_CHAIN_ID` | `1` | 以太坊链ID |
| `LOG_LEVEL` | `info` | 日志级别 |
| `LOG_FORMAT` | `json` | 日志格式 |

## 快速开始

### 1. 安装依赖

```bash
cd biz_services/account
go mod tidy
```

### 2. 启动服务

```bash
go run main.go
```

### 3. 测试接口

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "signature": "0x...",
    "message": "Please sign this message to login",
    "address": "0x..."
  }'
```

## 以太坊签名验证

服务使用标准的以太坊消息签名验证流程：

1. **消息哈希**: 使用以太坊标准前缀 `\x19Ethereum Signed Message:\n{length}{message}`
2. **签名格式**: 65字节的签名 (r: 32字节, s: 32字节, v: 1字节)
3. **地址恢复**: 从签名中恢复公钥并生成以太坊地址
4. **地址验证**: 验证恢复的地址与提供的地址是否匹配

## 开发说明

### 添加新的API接口

1. 在 `internal/models/` 中定义请求/响应结构体
2. 在 `internal/handlers/` 中实现处理器函数
3. 在 `main.go` 的 `setupRoutes` 函数中添加路由

### 错误处理

所有错误都通过统一的错误处理中间件处理，返回标准的JSON错误响应格式。

### 日志记录

服务使用Gin的日志中间件记录所有HTTP请求，包括请求方法、路径、状态码、延迟和客户端IP。
