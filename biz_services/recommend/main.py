import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware


sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'commons'))
import database
import defi_pool_service

app = FastAPI()

@app.get("/alphafi/agent/query/pool", summary="查询DeFi池信息接口", description="查询apy、tvlUsd、symbol、project信息")
async def query_defi_pools(
    sort_by: str = Query('apy', description="排序字段，可选值: apy, tvlUsd") ,
    sort_order: str = Query('desc', description="排序顺序，可选值: asc(升序), desc(降序)"),
    limit: int = Query(3, description="返回数据数量限制", ge=1),
    apy_low: float = Query(None, description="APY下限"),
    apy_high: float = Query(None, description="APY上限"),
    tvlUsd_low: float = Query(None, description="TVL USD下限"),
    tvlUsd_high: float = Query(None, description="TVL USD上限")
):
    """查询DeFi池信息接口

    Args:
        sort_by (str): 排序字段，默认为 'apy'
        sort_order (str): 排序顺序，默认为 'desc'
        limit (int): 返回数据数量限制，默认为 10
        apy_low (float, optional): APY下限
        apy_high (float, optional): APY上限
        tvlUsd_low (float, optional): TVL USD下限
        tvlUsd_high (float, optional): TVL USD上限

    Returns:
        list: 包含DeFi池信息的列表
    """
    # 验证排序字段
    valid_sort_fields = ['apy', 'tvlUsd']
    if sort_by not in valid_sort_fields:
        sort_by = 'apy'

    # 验证排序顺序
    valid_sort_orders = ['asc', 'desc']
    if sort_order.lower() not in valid_sort_orders:
        sort_order = 'desc'

    # 调用服务层获取数据
    result = await defi_pool_service.DefiPoolService.query_pools(
        sort_by=sort_by, 
        sort_order=sort_order, 
        limit=limit,
        apy_low=apy_low,
        apy_high=apy_high,
        tvlUsd_low=tvlUsd_low,
        tvlUsd_high=tvlUsd_high
    )

    return {
        "code": 200,
        "message": "success",
        "data": result
    }

@app.get("/alphafi/agent/recommend/pool", summary="推荐DeFi池信息", description="查询apy、tvlUsd、symbol、project信息")
async def recommend_defi_pools(
    project: str = Query(None, description="项目或协议名称"),
    symbol: str = Query(None, description="代币符号"),
    symbol_fuzzy: bool = Query(False, description="代币符号是否模糊匹配"),
    apy_low: float = Query(None, description="APY下限"),
    apy_high: float = Query(None, description="APY上限"),
    tvlUsd_low: float = Query(None, description="TVL USD下限"),
    tvlUsd_high: float = Query(None, description="TVL USD上限"),
    user_id: str = Query(None, description="钱包地址")
):
    
    result = await defi_pool_service.DefiPoolService.query_pools_v2(
        project=project, 
        symbol=symbol,
        symbol_fuzzy=symbol_fuzzy,
        apy_low=apy_low, 
        apy_high=apy_high,
        tvlUsd_low=tvlUsd_low, 
        tvlUsd_high=tvlUsd_high
    )
    
    return {
        "code": 200,
        "message": "success",
        "data": result
    }


async def init():
    await database.initialize_database()

if __name__ == "__main__":
    
    asyncio.run(init())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

    port = int(os.getenv('SERVER_PORT', 8000))
    uvicorn.run(
        app,
        host=os.getenv('SERVER_HOST', "0.0.0.0"),
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )